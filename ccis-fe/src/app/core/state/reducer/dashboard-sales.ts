
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { bindActionCreators } from "redux";
import { useDispatch } from "react-redux";
import { TSalesDashboard, TSalesDashboardPostPayload } from "@state/types";
import { ISalesDashboard } from "@interface/dashboard-sales";
import { createDynamicState } from "@helpers/array";

const initialState: TSalesDashboard = {
    salesDashboard: {
        data:{
            overallDonut: {
                total: 0,
                pendingTotal: 0,
            },
            proposalLineBar: [
                { label: "Jan", data: 0 },
                { label: "Feb", data: 0 },
                { label: "Mar", data: 0 },
                { label: "Apr", data: 0 },
                { label: "May", data: 0 },
                { label: "Jun", data: 0 },
                { label: "Jul", data: 0 },
                { label: "Aug", data: 0 },
                { label: "Sep", data: 0 },
                { label: "Oct", data: 0 },
                { label: "Nov", data: 0 },
                { label: "Dec", data: 0 },
            ],
            productSoldDonut: [
                { product: "Life Products", totalSold: 0 },
                { product: "Non-Life Products", totalSold: 0 },
                { product: "Micro Life Products", totalSold: 0 },
                { product: "Micro Non-Life Products", totalSold: 0 },
            ],
            proposalTable: [
                { cooperative_id: 0, city: "", province: "", coop_name: "", total: 0, pendingTotal: 0 },
            ],
        }
    },
    salesDashboardPayload: {},
    postSalesDashboard: createDynamicState(),
};

const salesDashboardSlice = createSlice({
    name: "salesDashboard",
    initialState,
    reducers: {
        postSalesDashboard(state, _action: PayloadAction<TSalesDashboardPostPayload>) {
            state.postSalesDashboard = createDynamicState(["loading"]);
        },
        postSalesDashboardSuccess(state, action: PayloadAction<ISalesDashboard>) {
            state.salesDashboard = action.payload;
            state.postSalesDashboard = createDynamicState(["success"]);
        },
        postSalesDashboardFailure(state) {
            state.postSalesDashboard = createDynamicState(["error"]);
        },
    },
});

export const { postSalesDashboard, postSalesDashboardSuccess, postSalesDashboardFailure } = salesDashboardSlice.actions;

export const useSalesDashboardActions = () => {
    return bindActionCreators(
        {
            postSalesDashboard,
        },
        useDispatch()
    );
};

export default salesDashboardSlice.reducer;