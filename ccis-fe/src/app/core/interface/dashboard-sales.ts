
export interface ISalesDashboard {
  data: {
    proposalLineBar: {
      label: string;
      data: number;
    }[];
    productSoldDonut: {
      product: string;
      totalSold: number;
    }[];
    proposalTable: {
      cooperative_id: number;
      city : string;
      province: string;
      coop_name: string;
      total: number;
      pendingTotal: number;
    }[];
    overallDonut: {
      // Add the properties for the overall donut chart
      total: number;
      pendingTotal: number;
    };
  }
}

export interface ISalesDashboardPostPayload {
  regionId?: number;
  province?: string;
  city?: string;
  cooperativeId?: number;
}
